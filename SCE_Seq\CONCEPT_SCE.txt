         %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

         The Shuffled Complex Evolution (SCE-UA) Method

         %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


                     %%%%%%%%%%%%%%%%%%%%%%

                     Concept Representation 

                     %%%%%%%%%%%%%%%%%%%%%%








                          Qingyun Duan
           Department of Hydrology and Water Resources
                      University of Arizona
                        Tucson, AZ 85721
                         (602) 621-9360
                   email:<EMAIL>




                           April, 1992


                   ==========================
                   Summary of Input Variables
                   ==========================

NGS    = number of complexes in a sample population
NPG    = number of points in each complex
NPT    = number of points in the entire sample population, 
         NPT=NGS*NPG
NPS    = number of points in each sub-complex
NSPL   = number of evolution steps allowed for each complex before 
         complex shuffling
MINGS  = minimum number of complexes required, if the number of 
         complexes NGS is allowed to reduce as the search proceeds
MAXN   = maximum number of trials allowed before optimization is 
         terminated
KSTOP  = number of shuffling loops in which the criterion value
         must change by PECNTO before optimization is terminated
PECNTO = percentage by which the criterion value must change in 
         KSTOP shuffling loops



                   ==========================
                   Brief Description of Steps
                   ==========================

1.    Generate sample:  Sample NPT points in the feasible parameter
      space and compute the criterion value at each point.  In the
      absence of prior information, use a uniform probability
      distribution to generate a sample.

2.    Rank points:  Sort the NPT points in order of increasing
      criterion value so that the first point represents the point
      with the smallest criterion value and the last point
      represents the point with the largest criterion value.

3.    Partition into complexes:  Partition the NPT points into NGS
      complexes, each containing NPG points.  The complexes are
      partitioned in such a way that the first complex contains
      every NGS*(k-1)+1 ranked point, the second complex contains
      every NGS*(k-1)+2 ranked point, and so on, where k =
      1,2,...,NPG.

      Figure 1a shows that a sample population containing NPT (=10)
      points is divided into NGS (=2) complexes.  Each complex
      contains NPG (=5) points which are marked by (.) and *
      respectively.  The contour lines in Figures 1 and 2 represent
      a function surface that has a global optimum located 
      at (4,2) and a local optimum located at (1,2).

4.    Evolve each complex:  Evolve each complex independently by
      taking NSPL evolution steps.  Figure 2 illustrates how each
      evolution step is taken.

      In Figure 2, the black dots (.) indicate the locations of the
      points in a complex before the evolution step is taken.  A
      sub-complex containing NPS (=3, i.e., forms a triangle in
      this case) points is selected according to a prespecified
      probability distribution to initiate an evolution step.  The
      probability distribution is specified such that the better
      points have a higher chance of being chosen to form the
      sub-complex than the worse points.  The symbol (*) represents
      the new points generated by the evolution steps.  There are
      three types of evolution steps: reflection, contraction and
      mutation.  Figures 2a, 2b and 2d illustrate the "reflection"
      step, which is implemented by reflecting the worst point in
      a sub-complex through the centroid of the other points. 
      Since the reflected point has a lower criterion value than
      the worst point, the worst point is discarded and replaced
      by the new point.  Thus an evolution step is completed.  In
      Figure 2c, the new point is generated by a "contraction" step
      (the new point lies half-way between the worst point and the
      centroid of the other points), after rejecting a reflection
      step for not improving the criterion value.  In Figure 2e, 
      a "mutation" step is taken by randomly selecting a point in
      the feasible parameter space to replace the worst point of
      the sub-complex.  This is done after a reflection step is
      attempted, but results in a point outside of the feasible
      parameter space.  Another scenario in which a mutation step
      is taken is when both the reflection step and the contraction
      step do not improve the criterion value.  Figure 2f shows the
      final complex after NSPL (=5) evolution steps.

      Figure 1b shows the locations of the points in the two
      independently evolved complexes at the end of the first cycle
      of evolution.  It can be seen that one complex (marked by *)
      is converging toward the local optimum, while the other
      (marked by .) is converging toward the global optimum.

5.    Shuffle complexes:  Combine the points in the evolved
      complexes into a single sample population; sort the sample
      population in order of increasing criterion value; 
      re-partition or shuffle the sample population into NGS
      complexes according to the procedure specified in Step 3.

      Figure 1c displays the new membership of the two evolved
      complexes after shuffling.

6.    Check convergence:  If the number of trials have exceeded
      MAXN, or the criterion value has not improved by PECNTO*100
      percent in KSTOP shuffling loops, stop; else, continue.

7.    Check complex number reduction:  If MINGS < NGS, remove the
      complex with the lowest ranked points; set NGS=NGS-1 and
      NPT=NGS*NPG; and return to Step 4.  If MINGS=NGS, return to
      Step 4.

      Figure 1d exhibits the two complexes at the end of the second
      cycle of evolution.  It is clear that both complexes are
      converging to the global optimum at the end of second cycle.



                           ==========
                           REFERENCES
                           ==========


Duan, Q., A Global Optimization Strategy for Efficient and
      Effective Calibration of Hydrologic Models, Ph.D.
      dissertation, University of Arizona, Tucson, Arizona, 1991

Duan, Q., V.K. Gupta, and S. Sorooshian, A Shuffled Complex
      Evolution Approach for Effective and Efficient Global
      Minimization, Journal of Optimization Theory and Its
      Applications, Vol 61(3), 1993

Duan, Q., S. Sorooshian, and V.K. Gupta, Effective and Efficient
      Global Optimization for Conceptual Rainfall-Runoff Models,
      Water Resources Research, Vol 28(4), pp. 1015-1031, 1992


#################################################################

Figure 1. Illustration of the Shuffled Complex Evolution (SCE-UA)
          Method (attached separately)


Figure 2. Illustration of the Evolution Steps Taken by Each Complex
          (attached separately)

#################################################################
